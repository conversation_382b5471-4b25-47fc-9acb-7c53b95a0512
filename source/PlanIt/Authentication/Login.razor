﻿@page "/login"
@page "/login/{AuthKey}"

@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Authentication.Cookies
@using Microsoft.AspNetCore.Identity
@using System.Security.Claims
@using System.ComponentModel.DataAnnotations
@inject NavigationManager NavigationManager
@inject IHttpContextAccessor HttpContextAccessor
@inject IAuthenticationService AuthService
@inject IP4PlanClientProvider P4PlanClientProvider

<MudText Typo="Typo.h4" Align="Align.Center" GutterBottom="true">Connection to P4 Plan</MudText>

<div>
    <EditForm Model="@_loginModel" OnValidSubmit="Connect" FormName="LoginForm">
        <DataAnnotationsValidator />
        <ValidationSummary />

        <div class="mb-2">
            <label for="email">Email</label><br />
            <MudTextField id="email" class="form-control form-control-sm w-25" @bind-Value="_loginModel.Email" />
        </div>

        <div class="mb-2">
            <label for="password">Password</label><br />
            <MudTextField id="password" type="password" InputType="InputType.Password" class="form-control form-control-sm w-25" @bind-Value="_loginModel.Password" />
        </div>

        <MudButton ButtonType="ButtonType.Submit" Variant="Variant.Filled" Color="Color.Primary" Class="ml-auto">Connect</MudButton>
    </EditForm>
</div>

@if(!string.IsNullOrEmpty(errorMessage))
{
    <MudAlert Severity="Severity.Warning">@errorMessage</MudAlert>
}

@code {

    [Parameter]
    public string AuthKey { get; set; } = string.Empty;

    private string? errorMessage = string.Empty;

    [SupplyParameterFromForm]
    private LoginModel _loginModel { get; set; } = new();
    private string _errorMessage = string.Empty;

    private class LoginModel
    {
        [Required]
        [DataType(DataType.EmailAddress)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
        [Required]
        public string Password { get; set; } = string.Empty;
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        if (string.IsNullOrEmpty(AuthKey))
            return;

        var userNameToLogIn = AuthService.GetPreparedSignIn(AuthKey);

        if (string.IsNullOrEmpty(userNameToLogIn))
            return;

        var context = HttpContextAccessor.HttpContext;

        if (context is null)
        {
            errorMessage = "Null context";
            return;
        }

        var p4PlanClient = P4PlanClientProvider.GetP4PlanClient(userNameToLogIn);
        if (p4PlanClient is null)
        {
            errorMessage = "Bad credentials";
            return;
        }

        var fullName = await p4PlanClient.ConnectedUserName();

        var principal = AuthService.CreateUserClaims(userNameToLogIn, fullName);

        await context.SignInAsync(principal);
        NavigationManager.NavigateTo($"/todolist", true);
    }

    private async Task Connect()
    {
        try
        {
            _errorMessage = string.Empty;

            var p4PlanClient = await P4PlanClientProvider.Connect(_loginModel.Email, _loginModel.Password);
            if (p4PlanClient is null)
            {
                _errorMessage = "Bad credentials";
                return;
            }

            var fullName = await p4PlanClient.ConnectedUserName();

            var key = AuthService.PrepareSignIn(_loginModel.Email);
            NavigationManager.NavigateTo($"/login/{key}", true);
        }
        catch (Exception)
        {
            _errorMessage = $"Login failed. Please check your credentials and try again.";
        }
    }
}