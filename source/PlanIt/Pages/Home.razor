﻿@page "/"
@using System.Security.Claims
@using PlanIt.Authentication
@using PlanIt.Services.Authentication
@inject IP4PlanClientProvider P4PlanClientProvider
@inject IHttpContextAccessor HttpContextAccessor
@inject NavigationManager NavigationManager
@inject IAuthenticationService AuthService

<PageTitle>Home</PageTitle>

<h1>PlanIt</h1>

@if ((HttpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false) && P4PlanClientProvider.GetP4PlanClient(HttpContextAccessor.HttpContext?.User.Claims.First(c => c.Type == ClaimTypes.Email).Value) != null)
{
    <p>Connected to P4 Plan as @HttpContextAccessor.HttpContext?.User.Claims.First(c => c.Type == ClaimTypes.Name).Value</p>
}
else
{
    <p>Please connect to P4 Plan</p>
    <MudButton Variant="Variant.Filled" Href="/login">Connect</MudButton>
}

@code {
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        // Auto-authenticate in development mode
        if (!IsUserAuthenticated() && AuthService is DevelopmentAuthenticationService devAuthService)
        {
            var context = HttpContextAccessor.HttpContext;
            if (context != null)
            {
                try
                {
                    await devAuthService.AutoAuthenticateAsync(context);
                    // Force a refresh to update the UI with authentication state
                    NavigationManager.NavigateTo("/", true);
                }
                catch (Exception ex)
                {
                    // Log the error
                    Console.WriteLine($"Auto-authentication failed: {ex.Message}");
                }
            }
        }
    }

    private bool IsUserAuthenticated()
    {
        return HttpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;
    }
}