@page "/details/{id}"
@using P4PlanLib.Model
@using System.Security.Claims
@inject IP4PlanClientProvider P4PlanClientProvider
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject IHttpContextAccessor HttpContextAccessor

@if (!(HttpContextAccessor.HttpContext?.User.Identity?.IsAuthenticated ?? false))
{
    <p>Please <NavLink href="/">Login to P4 Plan</NavLink> before accessing this dashboard</p>
}
else if (item == null)
{
    <p>Nothing to display/loading</p>
}
else
{
    <MudContainer>
        <MudGrid Spacing="12">
            <MudItem xs="9">
                <ItemDetails Item="item"
                             ShareButtonText="@shareButtonText"
                             ShareAsync="ShareAsync" />

                <ChildItems Items="parentChildren"
                            CurrentItem="item" />
            </MudItem>
            <MudItem xs="3">
                <CommentsSection Comments="comments"
                                 NewCommentText="@newCommentText"
                                 PostingComment="postingComment"
                                 PostCommentError="@postCommentError"
                                 OnPostComment="PostCommentAsync"
                                 OnNewCommentTextChanged="OnNewCommentTextChanged" />
            </MudItem>
        </MudGrid>
    </MudContainer>
}

@code {
    [Parameter]
    public string? id { get; set; }

    private Item? item;
    private List<Comment> comments = new();
    private string newCommentText = string.Empty;
    private bool postingComment = false;
    private string postCommentError = string.Empty;
    private string shareButtonText = "Share";
    private List<Item>? parentChildren = null;
    private P4PlanLib.IP4PlanClient? p4PlanClient;

    private async Task OnNewCommentTextChanged(string value)
    {
        newCommentText = value;
        StateHasChanged();
    }

    protected override async Task OnInitializedAsync()
    {
        if (HttpContextAccessor.HttpContext?.User.Identity?.IsAuthenticated ?? false)
        {
            var displayName = HttpContextAccessor.HttpContext?.User.Claims.First(c => c.Type == ClaimTypes.Name).Value;
            var email = HttpContextAccessor.HttpContext?.User.Claims.First(c => c.Type == ClaimTypes.Email).Value;
            p4PlanClient = P4PlanClientProvider.GetP4PlanClient(email);
            if (p4PlanClient is null)
            {
                Navigation.NavigateTo("/");
                return;
            }

            item = await p4PlanClient.GetBacklogItem(id);
        }
    }

    private async Task ShareAsync()
    {
        try
        {
            var currentUrl = Navigation.Uri;
            await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", currentUrl);

            shareButtonText = "Copied!";
            StateHasChanged();

            await Task.Delay(3000);
            shareButtonText = "Share";
            StateHasChanged();
        }
        catch (Exception)
        {
            shareButtonText = "Copy failed";
            StateHasChanged();

            await Task.Delay(3000);
            shareButtonText = "Share";
            StateHasChanged();
        }
    }

    private async Task PostCommentAsync(string commentText)
    {
        if (p4PlanClient is null)
            return;

        postCommentError = string.Empty;
        postingComment = true;
        try
        {
            if (!string.IsNullOrEmpty(id))
            {
                var success = await p4PlanClient.PostComment(id, commentText);
                if (success)
                {
                    newCommentText = string.Empty;
                    comments = await p4PlanClient.GetComments(id);
                }
                else
                {
                    postCommentError = "Failed to post comment.";
                }
            }
            else
            {
                postCommentError = "Invalid item id.";
            }
        }
        catch (Exception ex)
        {
            postCommentError = ex.Message;
        }
        postingComment = false;
        StateHasChanged();
    }
}