﻿@page "/todolist"
@using GraphQL
@using System.Security.Claims
@inject HttpClient Http
@inject IP4PlanClientProvider P4PlanClientProvider
@inject NavigationManager Navigation
@inject IHttpContextAccessor HttpContextAccessor

<PageTitle>Todolist</PageTitle>

<h1>My Todo list</h1>

@if(!HttpContextAccessor.HttpContext?.User.Identity?.IsAuthenticated ?? false)
{
    <p>Please <NavLink href="/">Login to P4 Plan</NavLink> before accessing this dashboard</p>
} else if(!loaded)
{
    <p>Loading...</p>
}
else
{
    <MudText>Ordered by Showstoppers > Sprint tasks > Open bugs not in sprint > Backlog</MudText>

    <MudTable Items="rankedList" Hover="true" Striped="true" Dense="true" Bordered="true" SortLabel="Sort by">
        <ColGroup>
            <col style="width: 50px;" />
            <col style="width: 50px;" />
            <col />
            <col style="width: 120px;" />
            <col style="width: 120px;" />
        </ColGroup>
        <HeaderContent>
            <MudTh><MudTableSortLabel SortBy="new Func<P4PlanLib.Model.Item, object>(x=>x.Rank)"><MudTooltip Text="Rank is a value for Showstopper -> Sprint Items -> Bugs -> Backlog">Rank</MudTooltip></MudTableSortLabel></MudTh>
            <MudTh>Type</MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<P4PlanLib.Model.Item, object>(x=>x.Name)">Name</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<P4PlanLib.Model.Item, object>(x=>x.BeautifulPriority)">Priority</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<P4PlanLib.Model.Item, object>(x=>x.EstimatedDays)">Hours remaining</MudTableSortLabel></MudTh>
        </HeaderContent>
        <RowTemplate>
            <MudTd DataLabel="Rank">@context.Rank</MudTd>
            <MudTd DataLabel="Type" Style=@($"{GetTypeBackgroundColor(context.Type)}")><MudTooltip Text="@context.Type"><Blazicon Svg="TaskTypeToIcon(context.Type)" /></MudTooltip></MudTd>
            <MudTd DataLabel="Name">
                @if (string.IsNullOrEmpty(context.SubprojectPath))
                {
                    <span><MudLink href=@($"/details/{context.Id}")>@context.Name</MudLink></span>
                }
                else
                {
                    <span>@context.SubprojectPath / <MudLink href=@($"/details/{context.Id}")>@context.Name</MudLink></span>
                }
            </MudTd>
            <MudTd DataLabel="Priority">@context.BeautifulPriority</MudTd>
            <MudTd DataLabel="Work remaining">@(context.WorkRemaining?.ToString("F1") ?? "0")</MudTd>
        </RowTemplate>
    </MudTable>

    <MudSpacer/>
}

@code {
    private List<P4PlanLib.Model.Item>? mySprintItems { get; set; }
    private List<P4PlanLib.Model.Item>? myOpenBugs { get; set; }
    private List<P4PlanLib.Model.Item>? myOpenShowstoppers { get; set; }
    private List<P4PlanLib.Model.Item>? myBacklog { get; set; }
    private List<P4PlanLib.Model.Item>? rankedList { get; set; }
    private bool loaded { get; set; } = false;
    private bool showEmptyCategories { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (HttpContextAccessor.HttpContext?.User.Identity?.IsAuthenticated ?? false && !loaded)
        {
            var displayName = HttpContextAccessor.HttpContext?.User.Claims.First(c => c.Type == ClaimTypes.Name).Value;
            var email = HttpContextAccessor.HttpContext?.User.Claims.First(c => c.Type == ClaimTypes.Email).Value;
            var p4PlanClient = P4PlanClientProvider.GetP4PlanClient(email);
            if (p4PlanClient is null)
            {
                Navigation.NavigateTo("/");
                return;
            }

            var comparer = new P4PlanLib.ItemComparer();

            myOpenBugs = await p4PlanClient.Search($"\"Item type\"=bug and Status!=Complete and Severity >\"Severity B\" and \"Assigned to\":\"{displayName}\"");
            myOpenBugs.ForEach(t => t.Type = "Bug");
            myOpenShowstoppers = await p4PlanClient.Search($"\"Item type\"=bug and Status!=Complete and Severity <=\"Severity B\" and \"Assigned to\":\"{displayName}\"");
            myOpenShowstoppers.ForEach(t => t.Type = "Showstopper");
            mySprintItems = await p4PlanClient.Search($"\"Item type\"=\"backlog item\" and Status!=Complete and \"Assigned to\":\"{displayName}\" and \"Committed to\":\"S30\"");
            mySprintItems.ForEach(t => t.Type = "Sprint");
            myBacklog = await p4PlanClient.Search($"\"Item type\"=\"backlog item\" and Status!=Complete and \"Assign tag\":\"{displayName}\"");
            myBacklog = myBacklog.Except(mySprintItems, comparer).ToList();
            myBacklog.ForEach(t => t.Type = "Backlog");

            rankedList = new List<P4PlanLib.Model.Item>();
            rankedList.AddRange(myOpenShowstoppers);
            rankedList.AddRange(mySprintItems.Except(rankedList, comparer));
            rankedList.AddRange(myOpenBugs.Except(rankedList, comparer));
            rankedList.AddRange(myBacklog.Except(rankedList, comparer));

            for(var i = 0 ; i < rankedList.Count ; i++)
            {
                rankedList[i].Rank = i + 1;
            }

            loaded = true;
            StateHasChanged();
        }
    }

    private void NavigateToDetails(string id)
    {
        Navigation.NavigateTo($"/details/{id}");
    }

    private SvgIcon TaskTypeToIcon(string type)
    {
        return type switch
        {
            "Showstopper" => Lucide.OctagonMinus,
            "Sprint" => Lucide.Rocket,
            "Backlog" => Lucide.Clipboard,
            "Bug" => Lucide.Bug,
            _ => Lucide.CircleQuestionMark
        };
    }

    private string GetTypeBackgroundColor(string type)
    {
        return type switch
        {
            "Showstopper" => "background: #EE8888;",
            "Bug" => "background: #EEEE88;",
            _ => ""
        };
    }
}
