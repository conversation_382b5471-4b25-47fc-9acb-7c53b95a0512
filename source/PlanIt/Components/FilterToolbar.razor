@typeparam TItem
@using MudBlazor
@using P4PlanLib.Model

<MudPaper Class="pa-4">
    <MudGrid>
        <MudItem xs="12" sm="12" md="12" lg="12">
            <MudStack Row="true" Spacing="1">
                <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@(()=>Toggle(nameof(Criteria.TopPriority)))" StartIcon="@Icons.Material.Filled.Star">
                    Top Priority
                </MudButton>
                <MudButton Variant="@((Criteria.NotEstimated) ? Variant.Filled : Variant.Outlined)" Color="Color.Secondary" OnClick="@(()=>Toggle(nameof(Criteria.NotEstimated)))" StartIcon="@Icons.Material.Filled.Timer">
                    Not Estimated
                </MudButton>
                <MudButton Variant="@((Criteria.ItemsAtRisk) ? Variant.Filled : Variant.Outlined)" Color="Color.Error" OnClick="@(()=>Toggle(nameof(Criteria.ItemsAtRisk)))" StartIcon="@Icons.Material.Filled.PriorityHigh">
                    Items At Risk
                </MudButton>
            </MudStack>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudSelect T="string" Label="Project" @bind-Value="Criteria.Project" Dense="true" Clearable="true" Searchable="true">
                @if (Projects != null)
                {
                    @foreach(var p in Projects) { <MudSelectItem Value="@p">@p</MudSelectItem> }
                }
            </MudSelect>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudSelect T="string" Label="Fix Version" @bind-Value="Criteria.FixVersion" Dense="true" Clearable="true" Searchable="true">
                @if (FixVersions != null)
                {
                    @foreach(var v in FixVersions) { <MudSelectItem Value="@v">@v</MudSelectItem> }
                }
            </MudSelect>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudSelect T="string" Label="Label" @bind-Value="Criteria.Label" Dense="true" Clearable="true" Searchable="true">
                @if (Labels != null)
                {
                    @foreach(var l in Labels) { <MudSelectItem Value="@l">@l</MudSelectItem> }
                }
            </MudSelect>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudSelect T="string" Label="Sprint" @bind-Value="Criteria.Sprint" Dense="true" Clearable="true" Searchable="true">
                @if (Sprints != null)
                {
                    @foreach(var s in Sprints) { <MudSelectItem Value="@s">@s</MudSelectItem> }
                }
            </MudSelect>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudTextField Label="Assignee" @bind-Value="Criteria.Assignee" Dense="true" Immediate="true" Adornment="Adornment.End" AdornmentIcon="@Icons.Material.Filled.Person" />
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudSelect T="string" Label="Status" @bind-Value="Criteria.Status" Dense="true" Clearable="true" Searchable="true">
                @if (Statuses != null)
                {
                    @foreach(var s in Statuses) { <MudSelectItem Value="@s">@s</MudSelectItem> }
                }
            </MudSelect>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudSelect T="string" Label="Priority" @bind-Value="Criteria.Priority" Dense="true" Clearable="true" Searchable="true">
                @if (Priorities != null)
                {
                    @foreach(var p in Priorities) { <MudSelectItem Value="@p">@p</MudSelectItem> }
                }
            </MudSelect>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudTextField Label="Contains Text" @bind-Value="Criteria.ContainsText" Dense="true" Immediate="true" />
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudDatePicker Label="Created Date" @bind-Date="Criteria.CreatedDate" DisableToolbar="false" />
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudNumericField TValue="double?" Label="Story Point" @bind-Value="Criteria.StoryPoint" Min="0" Immediate="true" />
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudSelect T="string" Label="Team" @bind-Value="Criteria.Team" Dense="true" Clearable="true" Searchable="true">
                @if (Teams != null)
                {
                    @foreach(var t in Teams) { <MudSelectItem Value="@t">@t</MudSelectItem> }
                }
            </MudSelect>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudSelect T="string" Label="High Level Status" @bind-Value="Criteria.HighLevelStatus" Dense="true" Clearable="true" Searchable="true">
                @if (HighLevelStatuses != null)
                {
                    @foreach(var s in HighLevelStatuses) { <MudSelectItem Value="@s">@s</MudSelectItem> }
                }
            </MudSelect>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudSelect T="string" Label="Risk" @bind-Value="Criteria.Risk" Dense="true" Clearable="true" Searchable="true">
                @if (Risks != null)
                {
                    @foreach(var r in Risks) { <MudSelectItem Value="@r">@r</MudSelectItem> }
                }
            </MudSelect>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudSelect T="string" Label="Commitment" @bind-Value="Criteria.Commitment" Dense="true" Clearable="true" Searchable="true">
                @if (Commitments != null)
                {
                    @foreach(var c in Commitments) { <MudSelectItem Value="@c">@c</MudSelectItem> }
                }
            </MudSelect>
        </MudItem>

        <MudItem xs="12">
            <MudStack Row="true" AlignItems="Center" JustifyContent="SpaceBetween">
                <div>
                    <MudButton Variant="Variant.Filled" Color="Color.Success" OnClick="@ApplyAsync">Apply</MudButton>
                    <MudButton Variant="Variant.Outlined" Color="Color.Default" OnClick="@Cancel">Cancel</MudButton>
                </div>
                <div>
                    <MudText Typo="Typo.body2">JQL: @JqlPreview</MudText>
                </div>
            </MudStack>
        </MudItem>
    </MudGrid>
</MudPaper>

@code {
    // Exposed properties/events
    [Parameter] public IEnumerable<TItem> Items { get; set; } // optional: the parent data
    [Parameter] public Func<TItem, FilterCriteria, bool> ItemPredicate { get; set; } // optional: how to test each item
    [Parameter] public EventCallback<FilterResult<TItem>> OnApply { get; set; } // returns criteria, jql, filtered items
    [Parameter] public ILookupService LookupService { get; set; } // allow injection by caller if they prefer, else inject below

    // injected fallback
    [Inject] private ILookupService _injectedLookupService { get; set; }

    private ILookupService Lookup => LookupService ?? _injectedLookupService;

    protected FilterCriteria Criteria { get; set; } = new FilterCriteria();
    protected string JqlPreview { get; set; } = string.Empty;

    // Dropdown collections loaded via lookup service
    IEnumerable<string> Projects, FixVersions, Labels, Sprints, Priorities, Statuses, Teams, HighLevelStatuses, Risks, Commitments;

    protected override async Task OnInitializedAsync()
    {
        // load dropdowns (graceful if Lookup is null)
        if (Lookup != null)
        {
            Projects = await SafeCall(() => Lookup.GetProjectsAsync());
            FixVersions = await SafeCall(() => Lookup.GetFixVersionsAsync());
            Labels = await SafeCall(() => Lookup.GetLabelsAsync());
            Sprints = await SafeCall(() => Lookup.GetSprintsAsync());
            Priorities = await SafeCall(() => Lookup.GetPrioritiesAsync());
            Statuses = await SafeCall(() => Lookup.GetStatusesAsync());
            Teams = await SafeCall(() => Lookup.GetTeamsAsync());
            HighLevelStatuses = await SafeCall(() => Lookup.GetHighLevelStatusesAsync());
            Risks = await SafeCall(() => Lookup.GetRisksAsync());
            Commitments = await SafeCall(() => Lookup.GetCommitmentsAsync());
        }
        BuildJqlPreview();
    }

    private async Task<IEnumerable<string>> SafeCall(Func<Task<IEnumerable<string>>> fn)
    {
        try
        {
            return await fn();
        }
        catch
        {
            return Array.Empty<string>();
        }
    }

    private void Toggle(string propName)
    {
        switch (propName)
        {
            case nameof(Criteria.TopPriority): Criteria.TopPriority = !Criteria.TopPriority; break;
            case nameof(Criteria.NotEstimated): Criteria.NotEstimated = !Criteria.NotEstimated; break;
            case nameof(Criteria.ItemsAtRisk): Criteria.ItemsAtRisk = !Criteria.ItemsAtRisk; break;
        }
        BuildJqlPreview();
    }

    private async Task ApplyAsync()
    {
        BuildJqlPreview();

        IEnumerable<TItem> filtered = Items;
        if (Items != null && ItemPredicate != null)
        {
            filtered = Items.Where(i => ItemPredicate(i, Criteria)).ToList();
        }

        var result = new FilterResult<TItem>
        {
            Criteria = CloneCriteria(Criteria),
            Jql = JqlPreview,
            FilteredItems = filtered
        };

        if (OnApply.HasDelegate)
            await OnApply.InvokeAsync(result);
    }

    private void Cancel()
    {
        Criteria.Reset();
        BuildJqlPreview();
    }

    private void BuildJqlPreview()
    {
        var parts = new List<string>();
        if (Criteria.TopPriority) parts.Add("priority = Highest");
        if (Criteria.NotEstimated) parts.Add("estimate IS EMPTY");
        if (Criteria.ItemsAtRisk) parts.Add("risk = High");
        if (!string.IsNullOrWhiteSpace(Criteria.Project)) parts.Add($"project = \"{Criteria.Project}\"");
        if (!string.IsNullOrWhiteSpace(Criteria.FixVersion)) parts.Add($"fixVersion = \"{Criteria.FixVersion}\"");
        if (!string.IsNullOrWhiteSpace(Criteria.Label)) parts.Add($"labels = \"{Criteria.Label}\"");
        if (!string.IsNullOrWhiteSpace(Criteria.Sprint)) parts.Add($"Sprint ~ \"{Criteria.Sprint}\"");
        if (!string.IsNullOrWhiteSpace(Criteria.Assignee)) parts.Add($"assignee = \"{Criteria.Assignee}\"");
        if (!string.IsNullOrWhiteSpace(Criteria.Status)) parts.Add($"status = \"{Criteria.Status}\"");
        if (!string.IsNullOrWhiteSpace(Criteria.Priority)) parts.Add($"priority = \"{Criteria.Priority}\"");
        if (!string.IsNullOrWhiteSpace(Criteria.ContainsText)) parts.Add($"text ~ \"{Criteria.ContainsText}\"");
        if (Criteria.CreatedDate.HasValue) parts.Add($"created >= \"{Criteria.CreatedDate:yyyy-MM-dd}\"");
        if (Criteria.StoryPoint.HasValue) parts.Add($"storyPoints = {Criteria.StoryPoint}");
        if (!string.IsNullOrWhiteSpace(Criteria.Team)) parts.Add($"team = \"{Criteria.Team}\"");
        if (!string.IsNullOrWhiteSpace(Criteria.HighLevelStatus)) parts.Add($"highLevelStatus = \"{Criteria.HighLevelStatus}\"");
        if (!string.IsNullOrWhiteSpace(Criteria.Risk)) parts.Add($"risk = \"{Criteria.Risk}\"");
        if (!string.IsNullOrWhiteSpace(Criteria.Commitment)) parts.Add($"commitment = \"{Criteria.Commitment}\"");

        JqlPreview = parts.Count == 0 ? "No filter" : string.Join(" AND ", parts);
    }

    private FilterCriteria CloneCriteria(FilterCriteria src)
    {
        return new FilterCriteria
        {
            TopPriority = src.TopPriority,
            NotEstimated = src.NotEstimated,
            ItemsAtRisk = src.ItemsAtRisk,
            Project = src.Project,
            FixVersion = src.FixVersion,
            Label = src.Label,
            Sprint = src.Sprint,
            Assignee = src.Assignee,
            Status = src.Status,
            Priority = src.Priority,
            ContainsText = src.ContainsText,
            CreatedDate = src.CreatedDate,
            StoryPoint = src.StoryPoint,
            Team = src.Team,
            HighLevelStatus = src.HighLevelStatus,
            Risk = src.Risk,
            Commitment = src.Commitment
        };
    }
}
