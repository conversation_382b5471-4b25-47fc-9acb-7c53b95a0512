@using P4PlanLib.Model

<div style="min-width: 0;">
    <!-- Header with title and share button -->
    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 1rem; gap: 1rem;">
        <h3 style="margin: 0; color: #1a1a1a; font-weight: 600; font-size: 1.5rem; line-height: 1.3; word-break: break-word;">
            @Item?.Name
        </h3>
        <button @onclick="ShareAsync" 
                style="padding: 0.6rem 1.2rem; 
                       border-radius: 8px; 
                       border: none; 
                       background: linear-gradient(135deg, #007acc, #005a99); 
                       color: white; 
                       cursor: pointer; 
                       font-size: 0.875rem; 
                       font-weight: 600; 
                       white-space: nowrap;
                       transition: all 0.2s ease;
                       box-shadow: 0 2px 4px rgba(0, 122, 204, 0.2);
                       min-width: 80px;"
                title="Copy link to clipboard">
            @ShareButtonText
        </button>
    </div>

    <!-- Details table with improved spacing -->
    <div style="border-radius: 12px; 
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); 
                border: 1px solid #e5e7eb; 
                overflow: hidden; 
                background: #fff;">
        
        <!-- Helix Plan App link -->
        <div style="display: flex; 
                    border-bottom: 1px solid #f0f0f0; 
                    background: #f8fafc;">
            <div style="flex: 0 0 160px; 
                        padding: 1rem; 
                        background: #f1f5f9; 
                        border-right: 1px solid #e5e7eb; 
                        font-weight: 600; 
                        color: #374151; 
                        font-size: 0.875rem;">
                Helix Plan App
            </div>
            <div style="flex: 1; 
                        padding: 1rem; 
                        color: #1f2937;">
                <a href="@Item?.GetItemLink()" 
                   target="_blank" 
                   style="color: #007acc; 
                          text-decoration: none; 
                          font-weight: 500;
                          transition: color 0.2s ease;"
                   onmouseover="this.style.color='#005a99'"
                   onmouseout="this.style.color='#007acc'">
                    Open in Helix Plan web app →
                </a>
            </div>
        </div>

        <!-- Assignee -->
        <div style="display: flex; 
                    border-bottom: 1px solid #f0f0f0;">
            <div style="flex: 0 0 160px; 
                        padding: 1rem; 
                        background: #f1f5f9; 
                        border-right: 1px solid #e5e7eb; 
                        font-weight: 600; 
                        color: #374151; 
                        font-size: 0.875rem;">
                Assignee
            </div>
            <div style="flex: 1; 
                        padding: 1rem; 
                        color: #1f2937;">
                <UserComponent Users="@GetAssigneeNames()" DisplayMode="UserComponent.UserDisplayMode.Tags" />
            </div>
        </div>

        <!-- Status grid -->
        <div style="display: grid; 
                    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); 
                    border-bottom: 1px solid #f0f0f0; 
                    background: #f8fafc;">
            <div style="padding: 0.75rem 1rem; 
                        border-right: 1px solid #e5e7eb; 
                        font-weight: 600; 
                        color: #374151; 
                        font-size: 0.875rem; 
                        background: #f1f5f9;">
                Priority
            </div>
            <div style="padding: 0.75rem 1rem; 
                        border-right: 1px solid #e5e7eb; 
                        font-weight: 600; 
                        color: #374151; 
                        font-size: 0.875rem; 
                        background: #f1f5f9;">
                Est. Days
            </div>
            <div style="padding: 0.75rem 1rem; 
                        border-right: 1px solid #e5e7eb; 
                        font-weight: 600; 
                        color: #374151; 
                        font-size: 0.875rem; 
                        background: #f1f5f9;">
                Status
            </div>
            @if (Item?.CommittedTo != null)
            {
                <div style="padding: 0.75rem 1rem; 
                            font-weight: 600; 
                            color: #374151; 
                            font-size: 0.875rem; 
                            background: #f1f5f9;">
                    Committed To
                </div>
            }
        </div>

        <div style="display: grid; 
                    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));">
            <div style="padding: 1rem; 
                        border-right: 1px solid #e5e7eb; 
                        color: #1f2937; 
                        font-weight: 500;">
                @(Item?.BeautifulPriority ?? "—")
            </div>
            <div style="padding: 1rem; 
                        border-right: 1px solid #e5e7eb; 
                        color: #1f2937; 
                        font-weight: 500;">
                @(Item?.EstimatedDays.ToString() ?? "—")
            </div>
            <div style="padding: 1rem; 
                        border-right: 1px solid #e5e7eb; 
                        color: #1f2937; 
                        font-weight: 500;">
                <StatusComponent Status="@Item?.Status" />
            </div>
            @if (Item?.CommittedTo != null)
            {
                <div style="padding: 1rem; 
                            color: #1f2937; 
                            font-weight: 500;">
                    <a href="@Item.CommittedTo.ItemLink" 
                       target="_blank" 
                       style="color: #007acc; 
                              text-decoration: none; 
                              font-weight: 500;
                              transition: color 0.2s ease;"
                       onmouseover="this.style.color='#005a99'"
                       onmouseout="this.style.color='#007acc'">
                        @Item.CommittedTo.Name →
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter]
    public Item? Item { get; set; }

    [Parameter]
    public string ShareButtonText { get; set; } = "Share";

    [Parameter]
    public EventCallback ShareAsync { get; set; }

    private IEnumerable<string> GetAssigneeNames()
    {
        if (Item?.AssignedTo != null && Item.AssignedTo is IList<AssignedTo> atList && atList.Count > 0)
        {
            return atList
                .Where(a => a != null && a.User != null && !string.IsNullOrWhiteSpace(a.User.Name))
                .Select(a => a.User!.Name!)
                .ToList();
        }
        return Enumerable.Empty<string>();
    }
}