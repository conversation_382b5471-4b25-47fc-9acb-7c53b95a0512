using System;
using System.Collections.Generic;

namespace PlanIt.Components;
public class FilterCriteria
{
    // Booleans for buttons
    public bool TopPriority { get; set; }
    public bool NotEstimated { get; set; }
    public bool ItemsAtRisk { get; set; }

    // Dropdowns / Comboboxes (accept multiple selections where appropriate)
    public string Project { get; set; }
    public string FixVersion { get; set; }
    public string Label { get; set; }
    public string Sprint { get; set; }
    public string Status { get; set; }
    public string Priority { get; set; }
    public string Team { get; set; }
    public string HighLevelStatus { get; set; }
    public string Risk { get; set; }
    public string Commitment { get; set; }

    // Text inputs
    public string Assignee { get; set; }
    public string ContainsText { get; set; }

    // Dates / numbers
    public DateTime? CreatedDate { get; set; }
    public double? StoryPoint { get; set; }

    // helper: reset
    public void Reset()
    {
        TopPriority = NotEstimated = ItemsAtRisk = false;
        Project = FixVersion = Label = Sprint = Status = Priority = Team = HighLevelStatus = Risk = Commitment = null;
        Assignee = ContainsText = null;
        CreatedDate = null;
        StoryPoint = null;
    }
}
