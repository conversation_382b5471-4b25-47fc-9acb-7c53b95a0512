@using P4PlanLib.Model

@if (Items != null && Items.Count > 0)
{
    <!-- Child Items Section -->
    <div>
        <h3 style="margin: 0 0 1rem 0; 
                   color: #1a1a1a; 
                   font-weight: 600; 
                   font-size: 1.25rem;">
            Child Items (@Items.Count)
        </h3>
        
        <div style="border-radius: 12px; 
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); 
                    border: 1px solid #e5e7eb; 
                    overflow: hidden; 
                    background: #fff;">
            
            <!-- Table header -->
            <div style="display: grid; 
                        grid-template-columns: 2fr 1fr 1.5fr; 
                        gap: 1rem; 
                        padding: 1rem 1.5rem; 
                        background: #f8fafc; 
                        border-bottom: 2px solid #e5e7eb;">
                <div style="font-weight: 600; 
                            color: #374151; 
                            font-size: 0.875rem;">
                    Name
                </div>
                <div style="font-weight: 600; 
                            color: #374151; 
                            font-size: 0.875rem;">
                    Status
                </div>
                <div style="font-weight: 600; 
                            color: #374151; 
                            font-size: 0.875rem;">
                    Assigned To
                </div>
            </div>
            
            <!-- Table rows -->
            <div>
                @foreach (var child in Items)
                {
                    <div style="display: grid; 
                                grid-template-columns: 2fr 1fr 1.5fr; 
                                gap: 1rem; 
                                padding: 1rem 1.5rem; 
                                border-bottom: 1px solid #f0f0f0; 
                                cursor: pointer; 
                                transition: all 0.2s ease;"
                         onmouseover="this.style.background='#f9fafb'"
                         onmouseout="this.style.background='transparent'">
                        
                        <!-- Item name -->
                        <div style="display: flex; 
                                    align-items: center; 
                                    gap: 0.75rem; 
                                    min-width: 0;">
                            <div style="width: 8px; 
                                        height: 8px; 
                                        border-radius: 50%; 
                                        background: #007acc; 
                                        flex-shrink: 0;"></div>
                            <span style="color: #007acc; 
                                         font-weight: 500; 
                                         font-size: 0.875rem; 
                                         text-decoration: none; 
                                         transition: color 0.2s ease;
                                         word-break: break-word;">
                                @child.Name
                            </span>
                        </div>
                        
                        <!-- Status -->
                        <div style="display: flex; 
                                    align-items: center;">
                            <StatusComponent Status="@child.Status" />
                        </div>
                        
                        <!-- Assigned users -->
                        <div style="display: flex; 
                                    align-items: center; 
                                    min-width: 0;">
                            <UserComponent Users="@GetChildAssigneeNames(child)" 
                                           DisplayMode="UserComponent.UserDisplayMode.Avatars" 
                                           MaxAvatars="3" />
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}
else
{
    <!-- Description section when no child items -->
    <div>
        <h3 style="margin: 0 0 1rem 0; 
                   color: #1a1a1a; 
                   font-weight: 600; 
                   font-size: 1.25rem;">
            Description
        </h3>
        
        <div style="border-radius: 12px; 
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); 
                    border: 1px solid #e5e7eb; 
                    background: #fff; 
                    overflow: hidden;">
            
            <div style="padding: 2rem;">
                @if (!string.IsNullOrWhiteSpace(CurrentItem?.Description))
                {
                    <div style="color: #374151; 
                                line-height: 1.6; 
                                white-space: pre-wrap; 
                                font-size: 0.875rem;">
                        @CurrentItem.Description
                    </div>
                }
                else
                {
                    <div style="text-align: center; 
                                color: #9ca3af; 
                                font-style: italic; 
                                padding: 2rem 0;">
                        <div style="font-size: 2rem; margin-bottom: 0.5rem;">📝</div>
                        <p style="margin: 0; font-size: 0.875rem;">No description available for this item.</p>
                    </div>
                }
            </div>
        </div>
    </div>
}

@code {
    [Parameter]
    public List<Item>? Items { get; set; }

    [Parameter]
    public Item? CurrentItem { get; set; }

    private IEnumerable<string> GetChildAssigneeNames(Item child)
    {
        if (child.AssignedTo != null && child.AssignedTo.Count() > 0)
        {
            return child.AssignedTo
                .Where(a => a.User?.Name != null)
                .Select(a => a.User!.Name!)
                .ToList();
        }
        return Enumerable.Empty<string>();
    }
}