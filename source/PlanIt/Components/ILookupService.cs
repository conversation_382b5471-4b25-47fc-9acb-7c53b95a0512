
namespace PlanIt.Components;
public interface ILookupService
{
    Task<IEnumerable<string>> GetProjectsAsync();
    Task<IEnumerable<string>> GetFixVersionsAsync();
    Task<IEnumerable<string>> GetLabelsAsync();
    Task<IEnumerable<string>> GetSprintsAsync();
    Task<IEnumerable<string>> GetPrioritiesAsync();
    Task<IEnumerable<string>> GetStatusesAsync();
    Task<IEnumerable<string>> GetTeamsAsync();
    Task<IEnumerable<string>> GetHighLevelStatusesAsync();
    Task<IEnumerable<string>> GetRisksAsync();
    Task<IEnumerable<string>> GetCommitmentsAsync();
}
