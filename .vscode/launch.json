{"version": "0.2.0", "configurations": [{"name": ".NET Core Launch (PlanIt)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/source/PlanIt/bin/Debug/net8.0/PlanIt.dll", "args": [], "cwd": "${workspaceFolder}/source/PlanIt", "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "launchBrowser": {"enabled": true, "args": "https://localhost:5001;http://localhost:5000"}}]}