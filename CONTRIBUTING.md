## Before getting started

If you read this, thanks ! I hope you get to work on this project and that you enjoy working with the team

# How to contribute

This project is an experimentation about paid opensource for the company. We would like freelance work to make this project move forward.

The team will answer to issue and PR comments. If you need further guidance, we can schedule a google meet on an already started subject, and if you become a regular we will grant you a guest access to our internal Slack

## Getting started

All current project priorities will be in the Issues section.

Each issue, once reviewed by the team, will have a time estimate. Unless there is a big gap between estimated time and actual work, this time estimate will serve as a basis for amount of money being paid for the task

You can assign to yourself 1 task at a time and once assigned, we expect the work to be done within an acceptable timeframe, being
- 1 week for a task with 1 day or less time estimate
- 2 weeks for a task with 2 to 4 days time estimate
- up to 3 weeks/1 month for larger 1 week time estimate tasks. **Don't grab these big tasks before the team agrees on it and you have already worked on smaller tasks on this project**

Note : 1d is 8 working hours

## How to work

For the project locally, and work on a task. Once you are satisfied with your code and the code quality has reached sufficient level, publish your branch to your repo, then start a Pull Request from your repo to the origin (this repo). Github UI on your front page should have the "Do you want to create a pull request?" button.

## Contact

You can contact the team in the issues section, or on LinkedIn
- [<PERSON>hez](https://www.linkedin.com/in/alexandre-trachez-8a647459/) project owner 
- [Tushar Patil](https://www.linkedin.com/in/29tusharpatil/) main point of contact in India, hiring

## Coding conventions

In order to sanitize coding standards, please follow [universal C# guidelines](https://learn.microsoft.com/en-us/dotnet/csharp/fundamentals/coding-style/coding-conventions).
