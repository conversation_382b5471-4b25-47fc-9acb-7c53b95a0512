# PlanIt

Project planning view extensions for P4Plan

> This is a project we are hiring for, and will pay for resolution of
> issues. Please see [Contributing](CONTRIBUTING.md)

Web application that aims at providing better suited views to Helix Plan/P4 Plan database for users of the software working in the gaming industry.

## 3 Goals

_What we aim to achieve_

- People who work on tasks (__Doers__) have a clear vision of the work expected from them. Todolist is clear, top bar display relevant information (between 3 and 20 items to display)
- People who organize the work (__Leads__) have a clear vision of the global sprint of the company. It has powerful filters and bulk edit options (between 40 and 100 items to display/summarize)
- People who monitor the work (__Producers/Management__) have a clear vision of the next milestone for the project. It has powerful filters and charts to display big data (above 200 items to display/summarize/chart)

## How to achieve these goals

- We don't redo everything
- Data storage is the main database from P4Plan, we only do UI and data reorganization/presentation
- We provide smart views, aggreagated views, practical UI
- We summarize data to fit users needs. We don't do too much generic, this is a dedicated tool to enhance a generic software

## Main scheduled features are:

- Custom todolist
- Common sprint view (assembling team sprints into project sprint)
- Dashboards with filtering and display
- Connexion with Swarm

Sceenshots on a dummy database:
![](references/Todolist.png)

![](references/Milestone.png)

